# Backend-bonding-dex-service

## 📌 Overview

The Backend-bonding-dex-service is a specialized microservice that powers the Libertum Bonding DEX (Decentralized Exchange) functionality. This service manages bonding curve mathematics, token operations, staking mechanisms, and monthly rewards distribution. It provides the core financial calculations and blockchain interactions for the bonding curve DEX protocol.

### Key Responsibilities

- **Bonding Curve DEX Operations**: Core DEX functionality with automated market making using bonding curves
- **Token Mathematics**: Complex mathematical calculations for token pricing, bonding, and unbonding
- **Staking Management**: Comprehensive staking operations and reward calculations
- **Monthly Rewards**: Automated monthly reward distribution system for stakers
- **Blockchain Integration**: Direct smart contract interactions for Base and BSC networks
- **Event Processing**: Real-time blockchain event monitoring and processing
- **HMAC Authentication**: Secure API authentication using HMAC signatures

## 🚀 Getting Started

### Prerequisites

- Node.js v18.0.0 or higher
- npm v8.0.0 or higher
- MongoDB (for data storage)
- Access to Base/BSC blockchain networks
- HMAC secret keys for authentication

### Installation

```bash
# Clone the repository
git clone https://github.com/Libertum-Project/backend-bonding-dex-service.git
cd backend-bonding-dex-service

# Install dependencies
npm install

# Setup environment variables
cp env.example .env
# Edit .env with your configuration values
```

### Environment Configuration

```bash
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGO_URI=mongodb://localhost:27017/libertum_bonding_dex

# Blockchain Configuration
BASE_RPC_URL=https://base-mainnet.infura.io/v3/your-project-id
BSC_RPC_URL=https://bsc-dataseed.binance.org/
PRIVATE_KEY=your-private-key-here

# Contract Addresses
BONDING_FACTORY_ADDRESS=0x...
STAKING_CONTROLLER_ADDRESS=0x...
TOKEN_CONTROLLER_ADDRESS=0x...

# Authentication
HMAC_SECRET=your-hmac-secret-here

# Event Processing
START_BLOCK=0
EVENT_BATCH_SIZE=1000

# Sentry (Error Tracking)
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=development
```

### Running Locally

```bash
# Development mode (with auto-reload)
npm run dev

# Production mode
npm run build
npm start

# The service will be available at:
# - API: http://localhost:5000
# - Swagger UI: http://localhost:5000/api-docs
# - Health Check: http://localhost:5000/health
```

## 🔐 Authentication

The Backend-bonding-dex-service uses HMAC (Hash-based Message Authentication Code) for secure API authentication:

### HMAC Authentication Process

1. Client creates a timestamp and request payload
2. Client generates HMAC signature using secret key
3. Client includes signature in `x-hmac-signature` header
4. Server validates signature against expected value
5. Request is processed if signature is valid

### HMAC Header Usage

```bash
# Include HMAC signature in requests
x-hmac-signature: sha256=generated-hmac-signature
Content-Type: application/json
```

### Generating HMAC Signature (Example)

```javascript
const crypto = require('crypto');

function generateHMACSignature(payload, secret) {
  const hmac = crypto.createHmac('sha256', secret);
  hmac.update(JSON.stringify(payload));
  return `sha256=${hmac.digest('hex')}`;
}
```

## 📡 API Documentation

**Swagger UI**: [http://localhost:5000/api-docs](http://localhost:5000/api-docs)

The API is organized into the following main categories:

### Bonding DEX APIs

- Token creation and management
- Bonding curve calculations
- Buy/sell operations
- Price discovery mechanisms
- Liquidity management

### Token Data APIs

- Token analytics and metrics
- Historical price data
- Volume and trading statistics
- Token holder information

### Staking APIs

- Stake token operations
- Unstake and withdraw functions
- Staking rewards calculation
- Staking pool management

### Calculate APIs

- Bonding curve mathematics
- Price calculation utilities
- Fee calculations
- ROI and yield calculations

### Monthly Rewards APIs (Admin)

- Reward distribution management
- Monthly reward calculations
- Reward claim mechanisms
- Administrative reward functions

## 📂 Directory Structure

```
backend-bonding-dex-service/
├── src/
│   ├── _grpc/                  # gRPC service definitions
│   │   ├── bonding.grpc.server.ts
│   │   └── proto/             # Protocol buffer definitions
│   ├── abis/                  # Smart contract ABIs
│   │   ├── bondingFactory.abi.json
│   │   ├── bondingTokenController.abi.json
│   │   └── stakingController.abi.json
│   ├── common/                # Common utilities
│   │   ├── response.handler.ts
│   │   └── return-message.ts
│   ├── config/                # Configuration files
│   │   ├── database.ts
│   │   └── swagger.ts
│   ├── controllers/           # API controllers
│   │   ├── bondingdex.controller.ts
│   │   ├── monthlyRewards.controller.ts
│   │   └── staking.controller.ts
│   ├── helpers/               # Helper functions
│   │   ├── bigMath.ts
│   │   ├── kafka.helper.ts
│   │   └── sentry.helper.ts
│   ├── middlewares/           # Express middleware
│   │   ├── hmac-middleware.ts
│   │   ├── logs-middleware.ts
│   │   └── sentry.middleware.ts
│   ├── models/                # Database models
│   │   ├── bondingdexs.model.ts
│   │   ├── eventBlocks.model.ts
│   │   └── stakingTransactions.model.ts
│   ├── repos/                 # Data access layer
│   │   ├── blocks.repo.ts
│   │   ├── monthlyRewards.repo.ts
│   │   └── stakingTransactions.repo.ts
│   ├── routes/                # API routes
│   │   ├── bondingdex.route.ts
│   │   ├── calculate.route.ts
│   │   └── monthlyRewards.route.ts
│   ├── services/              # Business logic services
│   │   ├── bondingdex.service.ts
│   │   ├── event.service.ts
│   │   └── staking.service.ts
│   ├── utils/                 # Utility functions
│   │   ├── common.interface.ts
│   │   ├── etherscan-util.ts
│   │   └── event.interface.ts
│   └── validations/           # Request validations
│       ├── BondingdexValidation.ts
│       └── StakingValidation.ts
├── Dockerfile                # Container configuration
├── package.json              # Dependencies and scripts
└── swagger.json              # OpenAPI specification
```

## ⚙️ Environment Variables

| Key                          | Description                         | Example Value                                    |
| ---------------------------- | ----------------------------------- | ------------------------------------------------ |
| `PORT`                       | Server port                         | `5000`                                           |
| `NODE_ENV`                   | Environment mode                    | `development`                                    |
| `MONGO_URI`                  | MongoDB connection string           | `mongodb://localhost:27017/libertum_bonding_dex` |
| `BASE_RPC_URL`               | Base blockchain RPC URL             | `https://base-mainnet.infura.io/v3/...`          |
| `BSC_RPC_URL`                | BSC blockchain RPC URL              | `https://bsc-dataseed.binance.org/`              |
| `PRIVATE_KEY`                | Blockchain private key              | `0x...`                                          |
| `BONDING_FACTORY_ADDRESS`    | Bonding factory contract address    | `0x...`                                          |
| `STAKING_CONTROLLER_ADDRESS` | Staking controller contract address | `0x...`                                          |
| `HMAC_SECRET`                | HMAC authentication secret          | `your-hmac-secret-here`                          |
| `START_BLOCK`                | Starting block for event processing | `0`                                              |
| `EVENT_BATCH_SIZE`           | Batch size for event processing     | `1000`                                           |
| `SENTRY_DSN`                 | Sentry error tracking DSN           | `https://...@sentry.io/...`                      |

## 🧪 Running Tests

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test suites
npm run test:unit
npm run test:integration

# Run tests in watch mode
npm run test:watch
```

## 🛠️ Technologies Used

- **Framework**: Express.js (Node.js web framework)
- **Database**: MongoDB with Mongoose ODM
- **Blockchain**: Web3.js for blockchain interactions
- **Authentication**: HMAC-based API authentication
- **Documentation**: Swagger/OpenAPI 3.0
- **Message Queue**: Kafka for event processing
- **Mathematics**: Big.js for precise decimal calculations
- **Error Tracking**: Sentry for production error monitoring
- **Validation**: Custom validation middleware
- **gRPC**: High-performance RPC communication
- **Docker**: Container support for deployment

## 🧑‍💻 Developer Notes

### Development Setup

1. Ensure MongoDB is running locally
2. Configure blockchain RPC endpoints in `.env`
3. Set up HMAC authentication secrets
4. Run `npm run dev` for development mode
5. API documentation is available at `/api-docs`

### Testing API Endpoints

- Use Postman or any API client
- Include HMAC signature in `x-hmac-signature` header
- Import the Swagger specification from `/api-docs.json`
- Test endpoints require valid HMAC authentication

### Common Development Tasks

```bash
# Generate new migration
npm run migration:generate

# Run database migrations
npm run migration:run

# Format code
npm run format

# Lint code
npm run lint

# Build for production
npm run build
```

### Mathematical Operations

- All token calculations use Big.js for precision
- Bonding curve formulas implement continuous token bonding
- Staking rewards calculated using time-weighted algorithms
- Price calculations account for slippage and fees

### Debugging

- Logs are structured using Winston logger
- Use `DEBUG=*` environment variable for detailed logs
- Check health endpoint: `GET /health`
- Monitor Sentry for production errors

## 📦 Build & Deploy

### Docker Deployment

```bash
# Build Docker image
docker build -t backend-bonding-dex-service .

# Run with Docker Compose
docker-compose up -d

# The service will be available at http://localhost:5000
```

### Production Deployment

```bash
# Install production dependencies
npm ci --only=production

# Build the application
npm run build

# Start the application
npm start
```

### Environment-Specific Configurations

- **Development**: Uses local MongoDB and testnet RPC
- **Staging**: Uses staging databases with testnet contracts
- **Production**: Uses production databases and mainnet contracts

## 🔧 API Endpoints Summary

| Category        | Endpoint                      | Method | Description                   |
| --------------- | ----------------------------- | ------ | ----------------------------- |
| **Health**      | `/health`                     | GET    | Service health check          |
| **Bonding DEX** | `/bondingdex/create`          | POST   | Create new bonding token      |
| **Bonding DEX** | `/bondingdex/buy`             | POST   | Buy tokens via bonding curve  |
| **Bonding DEX** | `/bondingdex/sell`            | POST   | Sell tokens via bonding curve |
| **Staking**     | `/staking/stake`              | POST   | Stake tokens                  |
| **Staking**     | `/staking/unstake`            | POST   | Unstake tokens                |
| **Calculate**   | `/calculate/price`            | GET    | Calculate token price         |
| **Rewards**     | `/monthly-rewards/distribute` | POST   | Distribute monthly rewards    |
| **Token Data**  | `/token-data/analytics`       | GET    | Get token analytics           |

## 📈 Key Features

### Bonding Curve Mathematics

- Continuous token bonding with configurable curves
- Automated price discovery based on supply and demand
- Slippage protection and fee calculations
- Reserve ratio management

### Staking Mechanisms

- Time-based staking rewards
- Compound interest calculations
- Early withdrawal penalties
- Flexible staking periods

### Monthly Rewards System

- Automated reward distribution
- Pro-rata reward calculations
- Gas-efficient bulk distributions
- Reward claim mechanisms

## 📞 Support

For technical support or questions about the Backend-bonding-dex-service:

- **Email**: <EMAIL>
- **Documentation**: https://docs.libertum.com
- **GitHub Issues**: https://github.com/Libertum-Project/backend-bonding-dex-service/issues

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/bonding-improvement`)
3. Commit your changes (`git commit -m 'Add bonding curve optimization'`)
4. Push to the branch (`git push origin feature/bonding-improvement`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
