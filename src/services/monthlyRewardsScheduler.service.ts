import cron, { ScheduledTask } from 'node-cron';
import monthlyRewardsService from './monthlyRewards.service';
import { <PERSON>rrorHandler } from '../helpers/sentry.helper';

export class MonthlyRewardsScheduler {
  private cronTask: ScheduledTask | null = null;

  /**
   * Start the monthly rewards calculation scheduler
   * Runs on the 1st of each month at 1:00 AM
   */
  public start(): void {
    if (this.cronTask) {
      console.warn('Monthly rewards scheduler is already running');
      return;
    }

    // Cron expression: 0 1 1 * * (At 01:00 on day-of-month 1)
    this.cronTask = cron.schedule(
      '0 1 1 * *',
      async () => {
        try {
          console.log('🏆 Starting monthly rewards calculation...');
          await monthlyRewardsService.calculateMonthlyAverages();
          console.log('✅ Monthly rewards calculation completed successfully');
        } catch (error) {
          console.error('❌ Failed to calculate monthly rewards:', error);
          ErrorHandler.handleError(error as Error, {
            context: 'Monthly rewards calculation failed',
            error,
          });
        }
      },
      {
        timezone: 'UTC', // Ensure consistent execution time
      },
    );

    console.log('✅ Monthly rewards scheduler started - will run on 1st of each month at 1:00 AM UTC');
  }

  /**
   * Stop the monthly rewards scheduler
   */
  public stop(): void {
    if (this.cronTask) {
      this.cronTask.stop();
      this.cronTask.destroy();
      this.cronTask = null;
      console.log('🛑 Monthly rewards scheduler stopped');
    }
  }

  /**
   * Check if scheduler is running
   */
  public isRunning(): boolean {
    return this.cronTask !== null;
  }

  /**
   * Manually trigger monthly calculation (for testing or admin use)
   * @param targetMonth Optional month in YYYY-MM format, defaults to previous month
   */
  public async triggerManualCalculation(targetMonth?: string): Promise<void> {
    try {
      console.log('🏆 Manually triggering monthly rewards calculation...');
      await monthlyRewardsService.calculateMonthlyAverages(targetMonth);
      console.log('✅ Manual monthly rewards calculation completed successfully');
    } catch (error) {
      console.error('❌ Manual monthly rewards calculation failed:', error);
      throw error;
    }
  }

  /**
   * Get scheduler status information
   */
  public getStatus(): { isRunning: boolean; nextRun: string | null } {
    const isRunning = this.isRunning();
    let nextRun: string | null = null;

    if (isRunning && this.cronTask) {
      // Calculate next run date (1st of next month at 1:00 AM UTC)
      const now = new Date();
      const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1, 1, 0, 0);
      nextRun = nextMonth.toISOString();
    }

    return {
      isRunning,
      nextRun,
    };
  }

  /**
   * Schedule a one-time calculation for a specific date (admin function)
   * @param date Date to run the calculation
   * @param targetMonth Month to calculate (YYYY-MM format)
   */
  public scheduleOneTimeCalculation(date: Date, targetMonth: string): void {
    const cronExpression = `${date.getMinutes()} ${date.getHours()} ${date.getDate()} ${date.getMonth() + 1} *`;

    const oneTimeTask = cron.schedule(cronExpression, async () => {
      try {
        console.log(`🏆 Running scheduled calculation for month ${targetMonth}...`);
        await monthlyRewardsService.calculateMonthlyAverages(targetMonth);
        console.log(`✅ Scheduled calculation for ${targetMonth} completed successfully`);
      } catch (error) {
        console.error(`❌ Scheduled calculation for ${targetMonth} failed:`, error);
        ErrorHandler.handleError(error as Error, {
          context: `Scheduled monthly rewards calculation failed for ${targetMonth}`,
          error,
        });
      } finally {
        // Destroy the one-time task after execution
        oneTimeTask.stop();
        oneTimeTask.destroy();
      }
    });

    console.log(`📅 One-time calculation scheduled for ${date.toISOString()} to process month ${targetMonth}`);
  }
}

export default new MonthlyRewardsScheduler();
