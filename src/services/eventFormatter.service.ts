import { Web3Helper } from '../helpers/web3.helper';
import eventFormats from '../utils/eventFormats.json';
import transactionsRepo from '../repos/transactions.repo';
import { calculate } from '../helpers/bigMath';
import stakingTransactionsRepo from '../repos/stakingTransactions.repo';
import { kafkaHelperService } from '../helpers/kafka.helper';
import monthlyRewardsRepo from '../repos/monthlyRewards.repo';
import { ErrorHandler } from '../helpers/sentry.helper';

class EventFormatterService {
  private getTransactionTimestamp = async (blockNumber: number, web3Instance: Web3Helper): Promise<number> => {
    try {
      const block = await web3Instance.getBlock(blockNumber);
      return Number(block.timestamp);
    } catch (error) {
      ErrorHandler.handleError(error as Error, {
        context: 'EventFormatterService.getTransactionTimestamp',
        blockNumber,
      });
      throw error;
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public processFormattedEvents = async (events: any, web3Instance: any): Promise<void> => {
    try {
      for (const event of events) {
        try {
          const formatTemplate = (eventFormats as any)[event.event];
          if (!formatTemplate) continue;

          const formatted: Record<string, any> = {};
          for (const [key, value] of Object.entries(formatTemplate)) {
            if (value === 'event') {
              formatted[key] = event.event;
            } else if (value === 'transactionHash') {
              formatted[key] = event.transactionHash;
            } else if (value === 'userAddress') {
              const fromAddress = await web3Instance.getTransactionAddresses(event?.transactionHash);
              formatted[key] = fromAddress;
            } else {
              formatted[key] = event.returnValues?.[value as string] ?? value;
            }
          }

          console.log(`Formatted Event: ${event.event}`, formatted);
          const timestamp = await this.getTransactionTimestamp(event.blockNumber, web3Instance);
          const createdAt = new Date(timestamp * 1000);

          if (formatted.type === 'BuyTokens' || formatted.type === 'SellTokens') {
            await transactionsRepo.insertTransaction({
              type: formatted.type,
              token: formatted.token,
              amount: calculate('div', formatted.amount, 10 ** 18),
              tradeID: formatted.tradeID,
              userAddress: formatted.userAddress,
              transactionHash: formatted?.transactionHash,
              createdAt: createdAt,
            });
          } else if (formatted.type === 'StakingContractCreated') {
            await transactionsRepo.insertStakingTransaction({
              bondingToken: formatted.bondingToken,
              stakingContract: formatted.stakingContract,
              salt: formatted.salt,
              transactionHash: formatted?.transactionHash,
            });

            console.log('staking transaction \n\n\n\n\n\n\n', {
              value: {
                bondingToken: formatted.bondingToken,
                stakingAddress: formatted.stakingContract,
                _id: formatted.salt,
                transactionHash: formatted?.transactionHash,
                isStaking: true,
              },
            });

            await kafkaHelperService.sendMessage('cron-to-user', [
              {
                value: JSON.stringify({
                  value: {
                    type: 'offering',
                    bondingToken: formatted.bondingToken,
                    stakingAddress: formatted.stakingContract,
                    _id: formatted.salt,
                    transactionHash: formatted?.transactionHash,
                    isStaking: true,
                  },
                }),
              },
            ]);
          } else if (formatted.type === 'TokenStaked' || formatted.type === 'TokenUnstaked') {
            await stakingTransactionsRepo.insertStakingTransaction({
              type: formatted.type,
              token: formatted.token,
              userAddress: formatted.userAddress,
              stakedAmount: formatted.stakedAmount,
              userTotalStaked: formatted.userTotalStaked,
              totalTokenStaked: formatted.totalTokenStaked,
              unstakedAmount: formatted.unstakedAmount,
              timestamp: Number(formatted.timestamp),
              actionID: formatted.actionID,
              transactionHash: formatted.transactionHash,
            });
          } else if (formatted.type === 'DividendPayout') {
            await monthlyRewardsRepo.updateDividendStatus({
              caller: formatted.caller,
              stableCoin: formatted.stableCoin,
              bondingToken: formatted.bondingToken,
              to: formatted.to,
              amounts: formatted.amounts,
              actionIDs: formatted.actionIDs,
            });
          }
        } catch (eventError) {
          ErrorHandler.handleError(eventError as Error, {
            context: 'EventFormatterService.processFormattedEvents.singleEvent',
            event: event.event,
            eventData: {
              transactionHash: event.transactionHash,
              blockNumber: event.blockNumber,
            },
          });
          // Continue processing other events even if one fails
          continue;
        }
      }
    } catch (error) {
      ErrorHandler.handleError(error as Error, {
        context: 'EventFormatterService.processFormattedEvents',
        eventCount: events?.length || 0,
      });
      throw error;
    }
  };
}

export default new EventFormatterService();
