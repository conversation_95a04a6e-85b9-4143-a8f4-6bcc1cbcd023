import { Types } from 'mongoose';
import { errorServiceResponse, successServiceResponse } from '../common/response.handler';
import { BONDINGDEX_MESSAGE } from '../common/return-message';
import { IBondingdex } from '../models/interface';
import { createLogs } from '../middlewares/logs-middleware';
import {
  // getTokenHoldersFromEtherscan,
  getTokenHoldersFromEtherscanPro,
} from '../utils/etherscan-util';
import { Bondingdex } from '../models/bondingdexs.model';
import transactionsModels from '../models/transactions.model';
import { ErrorHandler } from '../helpers/sentry.helper';

export const createBondingdex = async (data: IBondingdex) => {
  try {
    // Using findOneAndUpdate with upsert option
    // The first parameter is the filter criteria
    // You'll need to determine what field(s) to use as unique identifier
    const filter = {
      // Replace with appropriate unique identifier from your data
      // For example: id, name, or another unique field
      _id: data._id || new Types.ObjectId(),
    };

    const options = {
      new: true, // Return the updated document
      upsert: true, // Create document if it doesn't exist
      setDefaultsOnInsert: true, // Apply schema defaults for new documents
    };

    const inserted = await Bondingdex.findOneAndUpdate(filter, data, options);

    return successServiceResponse(BONDINGDEX_MESSAGE.INSERTED, inserted);
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'createBondingdex',
      data,
    });
    await createLogs({
      controller: 'bondingdex.controller',
      message: error.message || 'Error creating bondingdex',
      service: 'bondingdex.service',
      functionName: 'createBondingdex',
      data: `'${error}'`,
      payload: JSON.stringify(data),
    });
    return errorServiceResponse(error);
  }
};

export const getBondingdex = async (limit: number, offset: number, search: string = '', isBonded: boolean, upComming: boolean) => {
  console.log('=================limit', { limit, offset, isBonded, upComming }, typeof isBonded, typeof upComming);
  const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  try {
    const matchStage: any = upComming
      ? { status: 'PENDING', isSchedule: true }
      : {
          isDelete: false,
          ...(isBonded === false && { isBonded: false, status: 'APPROVED' }),
          ...(isBonded === true && { isBonded: true, status: 'APPROVED' }),
        };

    if (search?.trim()) {
      const regex = new RegExp(search, 'i');
      matchStage.$or = [
        {
          'overview.title': regex,
        },
        {
          'projectDetails.offeringName': regex,
        },
        {
          'projectDetails.assetName': regex,
        },
        {
          'projectDetails.tokenTicker': regex,
        },
      ];
    }

    const aggregationPipeline: any[] = [];

    if (Object.keys(matchStage).length) {
      aggregationPipeline.push({ $match: matchStage });
    }

    aggregationPipeline.push(
      { $sort: { updatedAt: -1 } },
      { $skip: offset - 1 },
      { $limit: limit },
      {
        $lookup: {
          from: 'transactions',
          localField: 'bondingAddress',
          foreignField: 'token',
          as: 'relatedTransactions',
        },
      },
      {
        $addFields: {
          raw24hVolume: {
            $sum: {
              $map: {
                input: {
                  $filter: {
                    input: '$relatedTransactions',
                    as: 'tx',
                    cond: {
                      $gte: ['$$tx.createdAt', twentyFourHoursAgo],
                    },
                  },
                },
                as: 'filteredTx',
                in: { $toDouble: '$$filteredTx.amount' },
              },
            },
          },
          '24hChange': {
            $cond: [
              {
                $and: [{ $gt: ['$projectDetails.latestNav', 0] }, { $gt: ['$projectDetails.navLaunchPrice', 0] }],
              },
              {
                $multiply: [
                  {
                    $divide: [
                      {
                        $subtract: ['$projectDetails.latestNav', '$projectDetails.navLaunchPrice'],
                      },
                      '$projectDetails.navLaunchPrice',
                    ],
                  },
                  100,
                ],
              },
              0,
            ],
          },
        },
      },
      {
        $addFields: {
          '24hVolume': {
            $cond: [
              { $gt: ['$projectDetails.bondingPrice', 0] },
              {
                $multiply: ['$raw24hVolume', '$projectDetails.bondingPrice'],
              },
              0,
            ],
          },
        },
      },

      {
        $addFields: {
          '24hVolume': { $round: ['$24hVolume', 2] },
          '24hChange': { $round: ['$24hChange', 2] },
        },
      },
      {
        $project: {
          _id: 1,
          tokenAddress: 1,
          updatedAt: 1,
          userId: 1,
          bondingAddress: 1,
          isStaking: 1,
          status: 1,
          isBonded: 1,
          stakingAddress: 1,
          createdAt: 1,
          documents: 1,
          fundAddress: 1,
          'overview.title': 1,
          'overview.icon': 1,
          'projectDetails.offeringName': 1,
          'projectDetails.startDate': 1,
          'projectDetails.endDate': 1,
          'projectDetails.tokenSupply': 1,
          'projectDetails.agentName': 1,
          'projectDetails.agentFunctions': 1,
          'projectDetails.poweredBy': 1,
          'projectDetails.poweredByLogo': 1,
          'projectDetails.bondingPrice': 1,
          'projectDetails.roi': 1,
          'projectDetails.launchValuation': 1,
          'projectDetails.previousValuation': 1,
          'projectDetails.navLaunchPrice': 1,
          'projectDetails.latestNav': 1,
          'projectDetails.acquisitionCosts': 1,
          'projectDetails.projectedYield': 1,
          '24hVolume': 1,
          '24hChange': 1,
          isSchedule: 1,
          scheduleTime: 1,
        },
      },
    );
    const result = await Bondingdex.aggregate(aggregationPipeline);
    const totalCount = await Bondingdex.countDocuments(matchStage);
    return successServiceResponse('Fetched Bondingdexs', {
      count: totalCount,
      rows: result,
    });
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getBondingdex',
      params: { limit, offset, search },
    });
    await createLogs({
      controller: 'bondingdex.controller',
      service: 'bondingdex.service',
      message: error.message || 'Error fetching bondingdex',
      functionName: 'getBondingdex',
      data: `'${error}'`,
      payload: JSON.stringify({ limit, offset, search }),
    });
    return errorServiceResponse(error.message);
  }
};

export const getBondingdexById = async (id: Types.ObjectId | string) => {
  try {
    const aggregationPipeline: any[] = [
      {
        $match: { _id: new Types.ObjectId(id) },
      },
      {
        $lookup: {
          from: 'transactions',
          localField: 'bondingAddress',
          foreignField: 'token',
          as: 'relatedTransactions',
        },
      },

      {
        $addFields: {
          transactionStats: {
            totalTxns: { $size: '$relatedTransactions' },
            totalBuys: {
              $size: {
                $filter: {
                  input: '$relatedTransactions',
                  as: 'tx',
                  cond: { $eq: ['$$tx.type', 'BuyTokens'] },
                },
              },
            },
            totalSells: {
              $size: {
                $filter: {
                  input: '$relatedTransactions',
                  as: 'tx',
                  cond: { $eq: ['$$tx.type', 'SellTokens'] },
                },
              },
            },
          },
        },
      },
      // ------
      // Fixed section for calculating volume
      {
        $addFields: {
          raw24hVolume: {
            $sum: {
              $map: {
                input: '$relatedTransactions', // Use all transactions without filtering
                as: 'tx',
                in: { $toDouble: '$$tx.amount' },
              },
            },
          },
        },
      },
      // Rename raw24hVolume to 24hVolume and round it
      {
        $addFields: {
          '24hVolume': { $round: ['$raw24hVolume', 2] },
        },
      },

      {
        $project: {
          relatedTransactions: 0, // Remove it from the result
          raw24hVolume: 0, // Also remove the intermediate field
        },
      },
    ];

    const result = await Bondingdex.aggregate(aggregationPipeline);

    if (!result || result.length === 0) {
      ErrorHandler.handleWarning('No record found', {
        context: 'getBondingdexById',
        id,
      });
      return errorServiceResponse('No record found');
    }

    return successServiceResponse(BONDINGDEX_MESSAGE.FETCH_DATA, result[0]);
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getBondingdexById',
      id,
    });
    await createLogs({
      controller: 'bondingdex.controller',
      message: error.message || 'Error fetching bondingdex by ID',
      service: 'bondingdex.service',
      functionName: 'getBondingdexById',
      data: `'${error}'`,
      payload: JSON.stringify({ id }),
    });
    return errorServiceResponse(error.message);
  }
};

export const getTokenHoldersService = async (contractAddress: string, page: number = 1, limit: number = 10) => {
  try {
    // const holdersPaginated = await getTokenHoldersFromEtherscan(contractAddress, page, offset);
    const holdersPaginated = await getTokenHoldersFromEtherscanPro(contractAddress, page, limit);
    return successServiceResponse('Token holders fetched successfully', holdersPaginated);
  } catch (error: any) {
    return errorServiceResponse(error.message);
  }
};

/**
 * Gets the count of unique users holding a specific token
 * @param token The token address to query for
 * @returns Promise containing the count of unique holders
 */
export const getUniqueTokenHolderCount = async (token: string): Promise<number> => {
  try {
    if (!token) {
      throw new Error('Token address is required');
    }

    // Use MongoDB aggregation to count unique userAddresses for the given token
    const result = await transactionsModels.aggregate([
      // Match all transactions with the specified token
      {
        $match: {
          token: { $regex: new RegExp(`^${token}$`, 'i') },
        },
      },
      // Group by userAddress to get unique addresses
      {
        $group: {
          _id: '$userAddress',
        },
      },
      // Count the unique addresses
      {
        $count: 'uniqueUsersCount',
      },
    ]);

    // Extract the count from the result
    // If no results found, return 0
    const uniqueUsersCount = result.length > 0 ? result[0].uniqueUsersCount : 0;

    return uniqueUsersCount;
  } catch (error) {
    console.error('Error getting unique token holder count:', error);
    throw error;
  }
};

/**
 * Get chart data for a specific token address with optional pagination
 * @param tokenAddress The bonding token address to get chart data for
 * @param page Optional page number for pagination (default: 1)
 * @param limit Optional limit of records per page (default: all records)
 * @returns Promise with chart data showing transaction history
 */

export const getTokenChartData = async (
  tokenAddress: string,
  period: string = '1d', // Default period is 1 day
  page?: number,
  limit?: number,
) => {
  try {
    // Validate input
    if (!tokenAddress) {
      ErrorHandler.handleWarning('Token address is required', {
        context: 'getTokenChartData',
        params: { tokenAddress, period, page, limit },
      });
      return errorServiceResponse('Token address is required');
    }

    // Set default values for pagination
    const currentPage = page && page > 0 ? page : 1;
    const itemsPerPage = limit && limit > 0 ? limit : 0; // 0 means no limit
    const skip = itemsPerPage > 0 ? (currentPage - 1) * itemsPerPage : 0;

    // Calculate start date based on period
    const endDate = new Date();
    let startDate = new Date();

    switch (period) {
      case '1d':
        startDate.setDate(endDate.getDate() - 1);
        break;
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '1m':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      case 'all':
        startDate = new Date(0); // Beginning of time
        break;
      default:
        startDate.setDate(endDate.getDate() - 1); // Default to 1 day
    }

    // First, get all transactions before the start date to calculate the initial cumulative value
    const previousTransactions = await transactionsModels.find(
      {
        token: { $regex: new RegExp(`^${tokenAddress}$`, 'i') },
        createdAt: { $lt: startDate },
      },
      null,
      { sort: { createdAt: 1 } },
    );

    // Calculate initial cumulative value from previous transactions
    let initialCumulativeValue = 0;
    for (const tx of previousTransactions) {
      const amount = parseFloat(tx.amount);
      const price = tx.price && tx.price !== '' ? parseFloat(tx.price) : 1;
      const amountValue = amount * price;
      const currentValue = tx.type === 'SellTokens' ? -1 * amountValue : amountValue;
      initialCumulativeValue += currentValue;
    }

    // Get transactions for the selected period
    const periodTransactions: any = await transactionsModels.find(
      {
        token: tokenAddress,
        createdAt: { $gte: startDate, $lte: endDate },
      },
      null,
      { sort: { createdAt: 1 } },
    );

    if (!periodTransactions || periodTransactions.length === 0) {
      return successServiceResponse('No transaction data found for the selected period', {
        chartData: [],
        initialValue: initialCumulativeValue,
        period,
        pagination: {
          total: 0,
          page: currentPage,
          limit: itemsPerPage,
          pages: 0,
        },
      });
    }

    // Process period transactions with initial cumulative value
    const chartData = [];
    let cumulativeValue = initialCumulativeValue;

    for (const tx of periodTransactions) {
      // Calculate value by multiplying amount by price
      const amount = parseFloat(tx.amount);
      const price = tx.price && tx.price !== '' ? parseFloat(tx.price) : 1;
      const amountValue = amount * price;

      // Adjust value based on transaction type
      const currentValue = tx.type === 'SellTokens' ? -1 * amountValue : amountValue;

      cumulativeValue += currentValue;

      chartData.push({
        time: tx.createdAt,
        type: tx.type,
        amount: amount,
        price: price,
        value: currentValue,
        cumulativeValue: cumulativeValue,
        tradeID: tx.tradeID,
        userAddress: tx.userAddress,
      });
    }

    // Sort by newest first for display if needed
    // Alternatively, you can keep chronological order for charting
    // chartData.sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());

    // Calculate pagination metadata
    const total = chartData.length;
    const pages = itemsPerPage > 0 ? Math.ceil(total / itemsPerPage) : 1;

    // Apply pagination if limit is provided
    const paginatedChartData = itemsPerPage > 0 ? chartData.slice(skip, skip + itemsPerPage) : chartData;

    return successServiceResponse('Chart data fetched successfully', {
      chartData: paginatedChartData,
      initialValue: initialCumulativeValue,
      period,
      pagination: {
        total,
        page: currentPage,
        limit: itemsPerPage,
        pages,
      },
    });
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getTokenChartData',
      params: { tokenAddress, period, page, limit },
    });
    await createLogs({
      controller: 'bondingdex.controller',
      message: error.message || 'Error in getTokenChartData',
      service: 'bondingdex.service',
      functionName: 'getTokenChartData',
      data: `'${error}'`,
      payload: JSON.stringify({ tokenAddress, period, page, limit }),
    });
    return errorServiceResponse(error.message);
  }
};
/**
 * Get token holder data showing net quantity held by each user address with pagination
 * @param tokenAddress The bonding token address to get holder data for
 * @param page Optional page number for pagination (default: 1)
 * @param limit Optional limit of records per page (default: all records)
 * @returns Promise with holder data showing unique holders and their net quantities
 */
export const getTokenHolderData = async (tokenAddress: string, page?: number, limit?: number) => {
  try {
    // Validate input
    if (!tokenAddress) {
      return errorServiceResponse('Token address is required');
    }

    // Set default values for pagination
    const currentPage = page && page > 0 ? page : 1;
    const itemsPerPage = limit && limit > 0 ? limit : 0; // 0 means no limit
    const skip = itemsPerPage > 0 ? (currentPage - 1) * itemsPerPage : 0;

    // Get all transactions for the token address
    const transactions: any = await transactionsModels.find({ token: tokenAddress }, null);
    if (!transactions || transactions.length === 0) {
      return successServiceResponse('No transaction data found', {
        holderData: [],
        pagination: {
          total: 0,
          page: currentPage,
          limit: itemsPerPage,
          pages: 0,
        },
      });
    }

    // Calculate net holdings per user address
    const holdingsMap = new Map();

    for (const tx of transactions) {
      const { userAddress, type, amount } = tx;
      const currentAmount = parseFloat(amount);

      // Get current balance or default to 0 if not found
      const currentBalance = holdingsMap.get(userAddress) || 0;

      // Update balance based on transaction type
      if (type === 'BuyTokens') {
        holdingsMap.set(userAddress, currentBalance + currentAmount);
      } else if (type === 'SellTokens') {
        holdingsMap.set(userAddress, currentBalance - currentAmount);
      }
    }

    // Convert map to array and filter out addresses with zero balance
    const allHolderData = Array.from(holdingsMap.entries())
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      .filter(([_, quantity]) => Math.abs(quantity) > 0.000001) // Filter with small epsilon to handle floating point issues
      .map(([userAddress, quantity]) => ({
        userAddress,
        quantity: parseFloat(quantity.toFixed(6)), // Format to 6 decimal places
      }));

    // Calculate pagination metadata
    const total = allHolderData.length;
    const pages = itemsPerPage > 0 ? Math.ceil(total / itemsPerPage) : 1;

    // Apply pagination if limit is provided
    const paginatedHolderData = itemsPerPage > 0 ? allHolderData.slice(skip, skip + itemsPerPage) : allHolderData;

    return successServiceResponse('Token holder data fetched successfully', {
      holderData: paginatedHolderData,
      pagination: {
        total,
        page: currentPage,
        limit: itemsPerPage,
        pages,
      },
    });
  } catch (error: any) {
    await createLogs({
      controller: 'bondingdex.controller',
      message: error.message || 'Error fetching token holder data',
      service: 'bondingdex.service',
      functionName: 'getTokenHolderData',
      data: `'${error}'`,
      payload: JSON.stringify({ tokenAddress, page, limit }),
    });
    return errorServiceResponse(error.message);
  }
};
