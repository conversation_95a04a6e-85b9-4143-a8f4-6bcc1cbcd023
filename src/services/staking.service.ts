import { errorServiceResponse, successServiceResponse } from '../common/response.handler';
import { BONDINGDEX_MESSAGE } from '../common/return-message';
import { ErrorHandler } from '../helpers/sentry.helper';
import { createLogs } from '../middlewares/logs-middleware';
import stakingTransactionsModels from '../models/stakingTransactions.model';
import { Bondingdex } from '../models/bondingdexs.model';
import monthlyRewardsModel from '../models/monthlyRewards.model';

interface PaginationParams {
  page: number;
  limit: number;
  search?: string;
}

interface StakingTransaction {
  type: string;
  token: string;
  userAddress: string;
  stakedAmount: string;
  unstakedAmount: string;
  userTotalStaked: string;
  totalTokenStaked: string;
  timestamp: number;
  actionID: string;
  transactionHash: string;
}

interface UserStakingSummary {
  userAddress: string;
  lastActivity: string;
  lastActivityTime: number;
  totalStakedAmount: string;
  averageAmount: string;
}

interface UserOfferingStake {
  token: string;
  offeringName: string;
  tokenTicker: string;
  totalStakedAmount: string;
  totalUnstakedAmount: string;
  netStakedAmount: string;
  lastActivityTime: number;
  transactionCount: number;
  overview: {
    title: string;
    description: string;
    logo: string;
    icon?: string;
  };
}

/**
 * Get staking transactions with pagination for a specific token
 */
export const getStakingTransactions = async (token: string, params: PaginationParams & { userAddress?: string; type?: string }): Promise<any> => {
  try {
    const { page = 1, limit = 10, search = '', userAddress, type } = params;
    const skip = (page - 1) * limit;

    // Build match conditions with case-insensitive matching
    const match: any = {
      token: { $regex: new RegExp(`^${token}$`, 'i') }, // Case-insensitive exact match for token
    };

    if (userAddress) {
      match.userAddress = { $regex: new RegExp(`^${userAddress}$`, 'i') }; // Case-insensitive exact match for userAddress
    }

    if (type) {
      match.type = type; // Fixed: was assigning userAddress instead of type
    }

    if (search) {
      match.$or = [{ userAddress: { $regex: search, $options: 'i' } }, { transactionHash: { $regex: search, $options: 'i' } }, { actionID: { $regex: search, $options: 'i' } }];
    }

    console.log('match', match);

    // Get total count and paginated data in parallel
    const [transactions, totalCount] = await Promise.all([stakingTransactionsModels.find(match).sort({ timestamp: -1 }).skip(skip).limit(limit).lean(), stakingTransactionsModels.countDocuments(match)]);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const nextPage = page < totalPages ? page + 1 : null;
    const previousPage = page > 1 ? page - 1 : null;

    // Format transactions for response
    const formattedTransactions: StakingTransaction[] = transactions.map((tx) => ({
      type: tx.type,
      token: tx.token,
      userAddress: tx.userAddress,
      stakedAmount: tx.stakedAmount || '0',
      unstakedAmount: tx.unstakedAmount || '0',
      userTotalStaked: tx.userTotalStaked || '0',
      totalTokenStaked: tx.totalTokenStaked || '0',
      timestamp: tx.timestamp,
      actionID: tx.actionID,
      transactionHash: tx.transactionHash,
    }));

    return successServiceResponse(BONDINGDEX_MESSAGE.FETCH_DATA, {
      transactions: formattedTransactions,
      currentPage: page,
      totalPages,
      totalCount,
      nextPage,
      previousPage,
    });
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getStakingTransactions',
      token,
      params,
    });
    await createLogs({
      controller: 'staking.controller',
      message: 'Error fetching staking transactions',
      service: 'staking.service',
      functionName: 'getStakingTransactions',
      data: `'${error}'`,
      payload: JSON.stringify({ token, params }),
    });
    return errorServiceResponse(error);
  }
};

/**
 * Get user staking summary with aggregated staked and unstaked amounts for a specific token
 */
export const getUserStakingSummary = async (token: string, params: PaginationParams): Promise<any> => {
  try {
    const { page = 1, limit = 10, search = '' } = params;
    const skip = (page - 1) * limit;

    // Build aggregation pipeline
    const pipeline: any[] = [
      { $match: { token: { $regex: new RegExp(`^${token}$`, 'i') } } },
      {
        $group: {
          _id: '$userAddress',
          lastActivity: { $last: '$type' },
          lastActivityTime: { $last: '$timestamp' },
          totalStakedAmount: {
            $sum: {
              $cond: [{ $eq: ['$type', 'TokenStaked'] }, { $toDouble: { $ifNull: ['$stakedAmount', '0'] } }, 0],
            },
          },
          totalUnstakedAmount: {
            $sum: {
              $cond: [{ $eq: ['$type', 'TokenUnstaked'] }, { $toDouble: { $ifNull: ['$unstakedAmount', '0'] } }, 0],
            },
          },
          transactionCount: { $sum: 1 },
        },
      },
      {
        $addFields: {
          netStakedAmount: { $subtract: ['$totalStakedAmount', '$totalUnstakedAmount'] },
          averageAmount: {
            $cond: [{ $gt: ['$transactionCount', 0] }, { $divide: ['$totalStakedAmount', '$transactionCount'] }, 0],
          },
        },
      },
    ];

    // Add search filter if provided
    if (search) {
      pipeline.push({
        $match: {
          _id: { $regex: search, $options: 'i' },
        },
      });
    }

    // Add sorting
    pipeline.push({ $sort: { lastActivityTime: -1 } });

    // Get total count
    const countPipeline = [...pipeline, { $count: 'totalCount' }];
    const [countResult] = await stakingTransactionsModels.aggregate(countPipeline);
    const totalCount = countResult?.totalCount || 0;

    // Add pagination to main pipeline
    pipeline.push({ $skip: skip }, { $limit: limit });

    // Execute aggregation
    const users = await stakingTransactionsModels.aggregate(pipeline);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const nextPage = page < totalPages ? page + 1 : null;
    const previousPage = page > 1 ? page - 1 : null;

    // Format users for response
    const formattedUsers: UserStakingSummary[] = users.map((user) => ({
      userAddress: user._id,
      lastActivity: user.lastActivity === 'TokenStaked' ? 'Stake' : 'Unstake',
      lastActivityTime: user.lastActivityTime,
      totalStakedAmount: user.netStakedAmount.toFixed(2),
      averageAmount: user.averageAmount.toFixed(2),
    }));

    return successServiceResponse(BONDINGDEX_MESSAGE.FETCH_DATA, {
      users: formattedUsers,
      currentPage: page,
      totalPages,
      totalCount,
      nextPage,
      previousPage,
    });
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getUserStakingSummary',
      token,
      params,
    });
    await createLogs({
      controller: 'staking.controller',
      message: 'Error fetching user staking summary',
      service: 'staking.service',
      functionName: 'getUserStakingSummary',
      data: `'${error}'`,
      payload: JSON.stringify({ token, params }),
    });
    return errorServiceResponse(error);
  }
};

/**
 * Get all unique offerings where user has staked
 */
export const getUserStakedOfferings = async (userAddress: string, params: PaginationParams): Promise<any> => {
  try {
    const { page = 1, limit = 10, search = '' } = params;
    const skip = (page - 1) * limit;

    // Aggregation pipeline to get unique tokens where user has staked
    const pipeline: any[] = [
      { $match: { userAddress: { $regex: new RegExp(`^${userAddress}$`, 'i') } } },
      {
        $group: {
          _id: '$token',
          totalStakedAmount: {
            $sum: {
              $cond: [{ $eq: ['$type', 'TokenStaked'] }, { $toDouble: { $ifNull: ['$stakedAmount', '0'] } }, 0],
            },
          },
          totalUnstakedAmount: {
            $sum: {
              $cond: [{ $eq: ['$type', 'TokenUnstaked'] }, { $toDouble: { $ifNull: ['$unstakedAmount', '0'] } }, 0],
            },
          },
          lastActivityTime: { $last: '$timestamp' },
          transactionCount: { $sum: 1 },
        },
      },
      {
        $addFields: {
          netStakedAmount: { $subtract: ['$totalStakedAmount', '$totalUnstakedAmount'] },
        },
      },
      {
        $match: {
          netStakedAmount: { $gt: 0 }, // Only include tokens with positive stake
        },
      },
      { $sort: { lastActivityTime: -1 } },
    ];

    // Execute aggregation to get staked tokens
    const stakedTokens = await stakingTransactionsModels.aggregate(pipeline);

    if (stakedTokens.length === 0) {
      return successServiceResponse(BONDINGDEX_MESSAGE.FETCH_DATA, {
        offerings: [],
        currentPage: page,
        totalPages: 0,
        totalCount: 0,
        nextPage: null,
        previousPage: null,
      });
    }

    // Get token addresses
    const tokenAddresses = stakedTokens.map((token) => token._id);

    // Build match condition for bondingdex search
    const bondingMatch: any = {
      $or: [{ bondingAddress: { $in: tokenAddresses } }, { stakingAddress: { $in: tokenAddresses } }],
    };

    // Add search filter if provided
    if (search) {
      bondingMatch.$and = [
        bondingMatch,
        {
          $or: [{ 'overview.title': { $regex: search, $options: 'i' } }, { 'projectDetails.offeringName': { $regex: search, $options: 'i' } }, { 'projectDetails.tokenTicker': { $regex: search, $options: 'i' } }],
        },
      ];
    }

    // Get bonding offerings with pagination
    const [offerings, totalCount] = await Promise.all([
      Bondingdex.find(bondingMatch)
        .select({
          'overview.title': 1,
          'overview.description': 1,
          'overview.logo': 1,
          'overview.icon': 1,
          'projectDetails.offeringName': 1,
          'projectDetails.tokenTicker': 1,
          'projectDetails.tokenDecimals': 1,
          'projectDetails.projectedYield': 1,
          bondingAddress: 1,
          stakingAddress: 1,
          fundAddress: 1,
        })
        .skip(skip)
        .limit(limit)
        .lean(),
      Bondingdex.countDocuments(bondingMatch),
    ]);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const nextPage = page < totalPages ? page + 1 : null;
    const previousPage = page > 1 ? page - 1 : null;

    // Merge staking data with offering data
    const formattedOfferings: UserOfferingStake[] = offerings.map((offering) => {
      const stakingData = stakedTokens.find((token) => token._id === offering.bondingAddress || token._id === offering.stakingAddress);

      return {
        token: offering.bondingAddress || offering.stakingAddress,
        offeringName: offering.projectDetails?.offeringName || 'N/A',
        tokenTicker: offering.projectDetails?.tokenTicker || 'N/A',
        totalStakedAmount: stakingData?.totalStakedAmount?.toFixed(2) || '0',
        totalUnstakedAmount: stakingData?.totalUnstakedAmount?.toFixed(2) || '0',
        netStakedAmount: stakingData?.netStakedAmount?.toFixed(2) || '0',
        lastActivityTime: stakingData?.lastActivityTime || 0,
        transactionCount: stakingData?.transactionCount || 0,
        overview: {
          title: offering.overview?.title || 'N/A',
          description: offering.overview?.description || 'N/A',
          logo: offering.overview?.logo || '',
          icon: offering.overview?.icon || '',
        },
        ...offering,
      };
    });

    return successServiceResponse(BONDINGDEX_MESSAGE.FETCH_DATA, {
      offerings: formattedOfferings,
      currentPage: page,
      totalPages,
      totalCount,
      nextPage,
      previousPage,
    });
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getUserStakedOfferings',
      userAddress,
      params,
    });
    await createLogs({
      controller: 'staking.controller',
      message: 'Error fetching user staked offerings',
      service: 'staking.service',
      functionName: 'getUserStakedOfferings',
      data: `'${error}'`,
      payload: JSON.stringify({ userAddress, params }),
    });
    return errorServiceResponse(error);
  }
};

/**
 * Get all unique users who staked in offering in a given month
 */
export const getStakingDividendList = async (token: string, monthString: string, params: PaginationParams): Promise<any> => {
  try {
    const { page = 1, limit = 10 } = params;
    const skip = (page - 1) * limit;

    const month = new Date(monthString).toISOString().slice(0, 7); // "2025-06"
    const query = {
      token: { $regex: new RegExp(`^${token}$`, 'i') }, // Case-insensitive exact match for token
      month,
    };

    console.log('query', query);

    const [data, totalCount] = await Promise.all([monthlyRewardsModel.find(query).skip(skip).limit(limit).lean(), monthlyRewardsModel.countDocuments(query)]);

    console.log('data', data);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const nextPage = page < totalPages ? page + 1 : null;
    const previousPage = page > 1 ? page - 1 : null;

    return successServiceResponse(BONDINGDEX_MESSAGE.FETCH_DATA, {
      data: data,
      currentPage: page,
      totalPages,
      totalCount,
      nextPage,
      previousPage,
    });
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getStakingDividendList',
      token,
      monthString,
      params,
    });
    await createLogs({
      controller: 'staking.controller',
      message: 'Error fetching staking dividend list',
      service: 'staking.service',
      functionName: 'getStakingDividendList',
      data: `'${error}'`,
      payload: JSON.stringify({ token, monthString, params }),
    });
    return errorServiceResponse(error);
  }
};
