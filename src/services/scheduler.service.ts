import cron, { ScheduledTask } from 'node-cron';
import { Web3Helper } from '../helpers/web3.helper';
import { EventService } from './event.service';
import { contractsConfig } from '../utils/event.interface';
import { ErrorHandler } from '../helpers/sentry.helper';

export class Scheduler {
  private eventService: EventService;
  private chain: string;
  private web3Helper: Web3Helper;
  private cronTask: ScheduledTask | null = null;

  constructor(
    chain: string,
    private rpcUrl: string,
  ) {
    this.chain = chain;
    this.web3Helper = new Web3Helper(this.rpcUrl);
    this.eventService = new EventService(this.web3Helper);
  }

  public start(): void {
    if (this.cronTask) {
      console.warn(`Scheduler for ${this.chain} is already running`);
      return;
    }

    this.cronTask = cron.schedule('*/10 * * * * *', async () => {
      for (const contract of contractsConfig) {
        try {
          const abi: any = contract.abi;
          const address = contract.address;
          const contractInstance = this.web3Helper.createContractInstance(abi, address);
          await this.eventService.fetch(this.chain, contract, contractInstance);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error(`Failed to fetch events for ${contract.name}: ${errorMessage}`);

          // Only log detailed error to sentry, not console
          ErrorHandler.handleError(error as Error, {
            context: `Failed to fetch events for ${contract.name}`,
            chain: this.chain,
            contractName: contract.name,
            contractAddress: contract.address,
          });
        }
      }
    });

    console.log(`✅ Scheduler started for ${this.chain}`);
  }

  public stop(): void {
    if (this.cronTask) {
      this.cronTask.stop();
      this.cronTask.destroy();
      this.cronTask = null;
      console.log(`🛑 Scheduler stopped for ${this.chain}`);
    }
  }

  public isRunning(): boolean {
    return this.cronTask !== null;
  }
}
