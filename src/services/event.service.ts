import { <PERSON>3Helper } from '../helpers/web3.helper';
import { IContract } from '../utils/event.interface';
import BlocksRepo from '../repos/blocks.repo';
import EventFormatterService from './eventFormatter.service';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from '../helpers/sentry.helper';

export class EventService {
  constructor(private web3Helper: Web3Helper) {}

  public async fetch(chain: string, Contract: IContract, contractInstance: any) {
    try {
      const address = Contract.address;
      //Block batch size  & start and end block calculation begins
      const eventBatchSize: number = Number(process.env.EVENT_BATCH_SIZE) || 1000;
      //getting current block number from blockchain
      const currentBlock = await this.web3Helper.getBlockNumber();
      //getting block info from database
      const lastFetchedBlock = await BlocksRepo.getLastFetchedBlock(chain, address);
      if (lastFetchedBlock <= currentBlock) {
        const fromBlock = lastFetchedBlock ? lastFetchedBlock : Contract.startBlock;
        // if start block plus batch size is greater than current block then it will set end block to current block
        const toBlock = fromBlock + eventBatchSize > currentBlock ? currentBlock : fromBlock + eventBatchSize;
        console.log({ fromBlock: fromBlock, toBlock: toBlock });
        if (fromBlock > currentBlock) {
          console.warn('\n\n ⚠️  fromBlock is greater than currentBlock\n\n');
          return;
        }
        const event = await this.web3Helper.getPastEvents(contractInstance, fromBlock, toBlock);
        //format data and send in queue
        EventFormatterService.processFormattedEvents(event, this.web3Helper);
        await BlocksRepo.upsertBlockNumber({
          chain: chain,
          address: Contract.address,
          contractName: Contract.name,
          blockNumber: toBlock + 1,
        });
        console.log('===============================');
      } else {
        console.log('waiting for blockNumber to update!!!');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`EventService.fetch error: ${errorMessage}`);

      ErrorHandler.handleError(error as Error, {
        context: 'EventService.fetch',
        chain,
        contractAddress: Contract.address,
        contractName: Contract.name,
      });

      // Don't throw error for API issues, just log them
      if (errorMessage.includes('invalid json response') || errorMessage.includes('FetchError')) {
        console.warn(`RPC temporarily unavailable for ${Contract.name}, will retry on next cycle`);
        return;
      }

      throw error;
    }
  }
}
