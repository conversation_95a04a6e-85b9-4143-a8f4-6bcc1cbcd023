import { Request, Response } from 'express';
import { createBondingdex, getBondingdex, getBondingdexById, getTokenChartData, getTokenHoldersService } from '../services/bondingdex.service';
import { errorServiceResponse, failResponse, successResponse } from '../common/response.handler';
import { getHolderCountFromEtherscan } from '../utils/etherscan-util';
import { <PERSON>rror<PERSON>and<PERSON> } from '../helpers/sentry.helper';

export const registerBondingdex = async (req: Request, response: Response) => {
  try {
    const result: any = await createBondingdex(req?.body);
    if (!result.error) {
      return successResponse(result.message, result?.data, response);
    } else {
      throw errorServiceResponse(result?.message);
    }
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'registerBondingdex',
      requestBody: req.body,
    });
    return failResponse(true, error?.message, response);
  }
};

export const listBondingdexs = async (req: Request, response: Response) => {
  try {
    // Extract and validate query parameters
    const limit = !isNaN(Number(req.query.limit)) ? Number(req.query.limit) : 10;
    const offset = !isNaN(Number(req.query.offset)) ? Number(req.query.offset) : 1;
    const search = req.query.search?.toString() || '';

    // Convert string query parameter to boolean
    const isBonded: any = req.query.isBonded;
    const upComming: any = req.query.upComming;
    // Fetch data with pagination
    const result: any = await getBondingdex(limit, offset, search, isBonded, upComming);

    if (!result.error) {
      // Check if we have results with count greater than 0
      if (result.data && result.data.count > 0 && result.data.rows && result.data.rows.length > 0) {
        // Loop through each row and update the holder count
        for (let i = 0; i < result.data.rows.length; i++) {
          try {
            // Get the token address from the current row
            const bondingAddress = result.data?.rows[i]?.bondingAddress;

            // Call getHolderCountFromEtherscan function with the token address
            const holderCountResult = await getHolderCountFromEtherscan(bondingAddress);

            // Update the totalHolders value with the actual count from Etherscan
            if (holderCountResult) {
              result.data.rows[i].totalHolders = holderCountResult?.totalCount || 0;
            }
          } catch (holderCountError) {
            ErrorHandler.handleError(holderCountError as Error, {
              context: 'listBondingdexs.holderCount',
              bondingAddress: result.data.rows[i].tokenAddress,
            });
            // Keep the existing totalHolders value if there's an error
          }
        }
      }

      return successResponse(result?.message, result?.data, response);
    } else {
      throw errorServiceResponse(result?.message);
    }
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'listBondingdexs',
      queryParams: req.query,
    });
    return failResponse(true, error?.message, response);
  }
};

export const getBondingdexs = async (_req: Request, response: Response) => {
  try {
    const { id }: any = _req.query; // Extracting ID from query params
    if (!id) {
      ErrorHandler.handleWarning('Missing ID parameter', {
        context: 'getBondingdexs',
        queryParams: _req.query,
      });
      return failResponse(true, 'ID is required', response);
    }
    const result: any = await getBondingdexById(id);
    if (!result.error) {
      return successResponse(result?.message, result?.data, response);
    } else {
      throw errorServiceResponse(result.message);
    }
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getBondingdexs',
      queryParams: _req.query,
    });
    return failResponse(true, error.message, response);
  }
};

export const getTokenHolders = async (req: Request, response: Response) => {
  try {
    const { contractAddress, page = 1, limit = 10 } = req.query;
    if (!contractAddress) {
      ErrorHandler.handleWarning('Missing contractAddress parameter', {
        context: 'getTokenHolders',
        queryParams: req.query,
      });
      return failResponse(true, 'contractAddress is required', response);
    }
    const result: any = await getTokenHoldersService(contractAddress as string, Number(page), Number(limit));
    if (!result.error) {
      return successResponse(result.message, result.data, response);
    } else {
      throw errorServiceResponse(result.message);
    }
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getTokenHolders',
      queryParams: req.query,
    });
    return failResponse(true, error.message, response);
  }
};

export const getTokenChartDataController = async (req: Request, res: Response) => {
  try {
    const { tokenAddress } = req.params;
    const period = (req.query.period as string) || '1d'; // Default to 1 day

    const page = req.query.page ? parseInt(req.query.page as string) : undefined;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : undefined;

    const result: any = await getTokenChartData(tokenAddress, period, page, limit);

    if (!result.error) {
      return successResponse(result.message, result.data, res);
    } else {
      throw errorServiceResponse(result.message);
    }
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getTokenChartDataController',
      params: req.params,
      queryParams: req.query,
    });
    return failResponse(true, error.message, res);
  }
};
