import { Request, Response } from 'express';
import monthlyRewardsService from '../services/monthlyRewards.service';
import monthlyRewardsScheduler from '../services/monthlyRewardsScheduler.service';
import { failResponse, successResponse } from '../common/response.handler';
import { <PERSON>rror<PERSON>andler } from '../helpers/sentry.helper';

/**
 * Get all pending reward distributions (not yet calculated by admin)
 */
export const getPendingRewards = async (req: Request, response: Response) => {
  try {
    const { token, month } = req.params;
    const pendingRewards = await monthlyRewardsService.getPendingRewards(token, month);
    return successResponse('Pending rewards retrieved successfully', pendingRewards, response);
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getPendingRewards',
      queryParams: req.query,
    });
    return failResponse(true, error?.message, response);
  }
};

/**
 * Get reward summary for a specific month and token
 */
export const getRewardSummary = async (req: Request, response: Response) => {
  try {
    const { month, token } = req.query;

    if (!month || !token) {
      ErrorHandler.handleWarning('Missing required parameters', {
        context: 'getRewardSummary',
        queryParams: req.query,
      });
      return failResponse(true, 'Month and token are required', response);
    }

    const summary = await monthlyRewardsService.getRewardSummary(month as string, token as string);

    if (!summary) {
      return failResponse(true, 'No reward data found for the specified month and token', response);
    }

    return successResponse('Reward summary retrieved successfully', summary, response);
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getRewardSummary',
      queryParams: req.query,
    });
    return failResponse(true, error?.message, response);
  }
};

/**
 * Set reward amount for a specific month and token (Admin function)
 */
export const setRewardAmount = async (req: Request, response: Response) => {
  try {
    const { month, token, totalRewardAmount } = req.body;

    if (!month || !token || totalRewardAmount === undefined) {
      ErrorHandler.handleWarning('Missing required parameters', {
        context: 'setRewardAmount',
        requestBody: req.body,
      });
      return failResponse(true, 'Month, token, and totalRewardAmount are required', response);
    }

    if (totalRewardAmount < 0) {
      return failResponse(true, 'Reward amount must be non-negative', response);
    }

    await monthlyRewardsService.setRewardAmount({
      month,
      token,
      totalRewardAmount: Number(totalRewardAmount),
    });

    // Get updated summary to return
    const updatedSummary = await monthlyRewardsService.getRewardSummary(month, token);

    return successResponse('Reward amount set successfully and distributed to users', updatedSummary, response);
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'setRewardAmount',
      requestBody: req.body,
    });
    return failResponse(true, error?.message, response);
  }
};

/**
 * Get user's reward history
 */
export const getUserRewards = async (req: Request, response: Response) => {
  try {
    const { userAddress } = req.params;

    if (!userAddress) {
      ErrorHandler.handleWarning('Missing userAddress parameter', {
        context: 'getUserRewards',
        params: req.params,
      });
      return failResponse(true, 'User address is required', response);
    }

    const userRewards = await monthlyRewardsService.getUserRewards(userAddress);

    return successResponse(
      'User rewards retrieved successfully',
      {
        userAddress,
        rewards: userRewards,
        totalRewards: userRewards.reduce((sum, reward) => sum + reward.userReward, 0),
      },
      response,
    );
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getUserRewards',
      params: req.params,
    });
    return failResponse(true, error?.message, response);
  }
};

/**
 * Manually trigger monthly calculation (Admin function)
 */
export const triggerMonthlyCalculation = async (req: Request, response: Response) => {
  try {
    const { targetMonth } = req.body;

    await monthlyRewardsScheduler.triggerManualCalculation(targetMonth);

    return successResponse(`Monthly calculation ${targetMonth ? `for ${targetMonth}` : ''} completed successfully`, { targetMonth: targetMonth || 'previous month' }, response);
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'triggerMonthlyCalculation',
      requestBody: req.body,
    });
    return failResponse(true, error?.message, response);
  }
};

/**
 * Get scheduler status
 */
export const getSchedulerStatus = async (req: Request, response: Response) => {
  try {
    const status = monthlyRewardsScheduler.getStatus();

    return successResponse('Scheduler status retrieved successfully', status, response);
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getSchedulerStatus',
    });
    return failResponse(true, error?.message, response);
  }
};

/**
 * Recalculate rewards for a specific month (Admin function)
 */
export const recalculateMonth = async (req: Request, response: Response) => {
  try {
    const { month } = req.body;

    if (!month) {
      ErrorHandler.handleWarning('Missing month parameter', {
        context: 'recalculateMonth',
        requestBody: req.body,
      });
      return failResponse(true, 'Month is required (format: YYYY-MM)', response);
    }

    await monthlyRewardsService.recalculateMonth(month);

    return successResponse(`Rewards for ${month} recalculated successfully`, { month }, response);
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'recalculateMonth',
      requestBody: req.body,
    });
    return failResponse(true, error?.message, response);
  }
};
