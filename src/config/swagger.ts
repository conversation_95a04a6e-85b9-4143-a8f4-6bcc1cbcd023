import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { Express } from 'express';

const options: swaggerJsdoc.Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Bonding DEX API Documentation',
      version: '1.0.0',
      description: 'API documentation for the Bonding DEX service',
      contact: {
        name: 'API Support',
        email: '<EMAIL>',
      },
    },
    servers: [
      // Dynamic servers will be set at runtime
    ],
    components: {
      securitySchemes: {
        hmacAuth: {
          type: 'apiKey',
          in: 'header',
          name: 'x-hmac-signature',
          description: 'HMAC signature for API authentication',
        },
      },
      schemas: {
        Bondingdex: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            userId: { type: 'string' },
            overview: {
              type: 'object',
              properties: {
                title: { type: 'string' },
                subTitle: { type: 'string' },
                description: { type: 'string' },
                entityName: { type: 'string' },
                entityType: { type: 'string' },
                webUrl: { type: 'string' },
                lineOfBusiness: { type: 'string' },
                sourceOfFunds: { type: 'string' },
                location: { type: 'string' },
                companyDescription: { type: 'string' },
                icon: { type: 'string' },
                cover: { type: 'string' },
                logo: { type: 'string' },
                propertyImages: {
                  type: 'array',
                  items: { type: 'string' },
                },
              },
            },
            projectDetails: {
              type: 'object',
              properties: {
                assetType: { type: 'string' },
                blockChainType: { type: 'string' },
                offeringType: { type: 'string' },
                tokenStandard: { type: 'string' },
                offeringName: { type: 'string' },
                CUSIP: { type: 'string' },
                isAuthorized: { type: 'boolean' },
                authorizedCountries: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      name: { type: 'string' },
                      isoCode: { type: 'string' },
                      countryCode: { type: 'string' },
                    },
                  },
                },
                startDate: { type: 'string', format: 'date-time' },
                endDate: { type: 'string', format: 'date-time' },
                minInvestment: { type: 'number' },
                maxInvestment: { type: 'number' },
                assetName: { type: 'string' },
                tokenTicker: { type: 'string' },
                tokenSupply: { type: 'number' },
                tokenDecimals: { type: 'number' },
                lockupMonths: { type: 'number' },
                holdTime: { type: 'string', format: 'date-time' },
                maxTokenHolding: { type: 'number' },
                navLaunchPrice: { type: 'number' },
                latestNav: { type: 'number' },
                isTransferAgent: { type: 'boolean' },
                taId: { type: 'string' },
                issuerId: { type: 'string' },
                issuerWallet: { type: 'string' },
                isPrivate: { type: 'boolean' },
                offeringMembers: {
                  type: 'array',
                  items: { type: 'string' },
                },
                customFields: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      label: { type: 'string' },
                      type: { type: 'string' },
                      value: { type: 'string' },
                    },
                  },
                },
                propertyType: { type: 'string' },
                propertySubtype: { type: 'string' },
                yearBuilt: { type: 'number' },
                lotSize: { type: 'number' },
                occupancy: { type: 'number' },
                projectedYield: { type: 'number' },
                launchValuation: { type: 'number' },
                previousValuation: { type: 'number' },
                deRatio: { type: 'number' },
                acquisitionCosts: {
                  type: 'object',
                  properties: {
                    legalCost: { type: 'number' },
                    estateAgent: { type: 'string' },
                    stampDuty: { type: 'string' },
                    tax: { type: 'string' },
                    platformFees: { type: 'number' },
                    maintenance: { type: 'string' },
                    total: { type: 'number' },
                  },
                },
                agentName: { type: 'string' },
                agentFunctions: {
                  type: 'array',
                  items: { type: 'string' },
                },
                poweredBy: { type: 'string' },
                poweredByLogo: { type: 'string' },
                bondingPrice: { type: 'number' },
                roi: { type: 'number' },
              },
            },
            documents: {
              type: 'object',
              properties: {
                eSign: { type: 'string' },
                pitchDeck: { type: 'string' },
                confidentialInformationMemorendum: { type: 'string' },
                landRegistration: { type: 'string' },
                titleDocs: { type: 'string' },
                bankApproval: { type: 'string' },
                encumbranceCertificate: { type: 'string' },
                propertyTaxReceipt: { type: 'string' },
                articlesOfAssociation: { type: 'string' },
                operatingAgreement: { type: 'string' },
                taxAssignmentLetter: { type: 'string' },
                certificateOfRegistration: { type: 'string' },
                registerOfManagers: { type: 'string' },
                customDocs: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      docsLabel: { type: 'string' },
                      value: { type: 'string' },
                    },
                  },
                },
              },
            },
            team: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  name: { type: 'string' },
                  title: { type: 'string' },
                  summary: { type: 'string' },
                  email: { type: 'string' },
                  url: { type: 'string' },
                  linkedInUrl: { type: 'string' },
                  twitterUrl: { type: 'string' },
                },
              },
            },
            isFinalSubmission: { type: 'boolean' },
            currentStep: { type: 'number' },
            isTokenDeploy: { type: 'boolean' },
            isFundDeploy: { type: 'boolean' },
            iserc20: { type: 'boolean' },
            tokenAddress: { type: 'string' },
            erc20Address: { type: 'string' },
            identityRegistry: { type: 'string' },
            fundAddress: { type: 'string' },
            fee: {
              type: 'object',
              properties: {
                escrowFee: { type: 'number' },
                wrapFee: { type: 'number' },
                dividendFee: { type: 'number' },
                redemptionFee: { type: 'number' },
              },
            },
            deployedDate: { type: 'string', format: 'date-time' },
            wrapperDeployedAt: { type: 'string', format: 'date-time' },
            isActive: { type: 'boolean' },
            isDelete: { type: 'boolean' },
            status: { type: 'string' },
            createdBy: { type: 'string' },
            template_id: { type: 'string' },
            envelopeId: { type: 'string' },
            offeringFeeStatus: { type: 'boolean' },
            reason: { type: 'string' },
            proposalHoldingPercentage: {
              type: 'array',
              items: { type: 'string' },
            },
            isBondingDeploy: { type: 'boolean' },
            isNft: { type: 'boolean' },
            bondingAddress: { type: 'string' },
          },
        },
        Error: {
          type: 'object',
          properties: {
            error: { type: 'boolean' },
            message: { type: 'string' },
            data: { type: 'null' },
          },
        },
        Success: {
          type: 'object',
          properties: {
            message: { type: 'string' },
            data: { type: 'object' },
          },
        },
      },
    },
  },
  apis: ['./src/routes/*.ts', './src/models/*.ts'], // Path to the API routes and models
};

const swaggerSpec = swaggerJsdoc(options);

export const setupSwagger = (app: Express) => {
  // Swagger UI with dynamic server URL
  app.use('/api-docs', swaggerUi.serve, (req, res, next) => {
    // Get the protocol and host from the request
    const protocol = req.protocol;
    const host = req.get('host');
    const baseUrl = `${protocol}://${host}`;

    // Create a dynamic swagger spec with the current host
    const dynamicSwaggerSpec = {
      ...swaggerSpec,
      servers: [
        {
          url: baseUrl,
          description: `Current server (${host})`,
        },
      ],
    };

    // Setup Swagger UI with dynamic spec
    swaggerUi.setup(dynamicSwaggerSpec)(req, res, next);
  });

  // Swagger JSON with dynamic server URL
  app.get('/api-docs.json', (req, res) => {
    // Get the protocol and host from the request
    const protocol = req.protocol;
    const host = req.get('host');
    const baseUrl = `${protocol}://${host}`;

    // Create a dynamic swagger spec with the current host
    const dynamicSwaggerSpec = {
      ...swaggerSpec,
      servers: [
        {
          url: baseUrl,
          description: `Current server (${host})`,
        },
      ],
    };

    res.setHeader('Content-Type', 'application/json');
    res.send(dynamicSwaggerSpec);
  });

  console.log('✅ Swagger documentation is available at /api-docs');
};
