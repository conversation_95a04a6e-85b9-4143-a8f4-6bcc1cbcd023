import { ContractAbi } from 'web3';
import BondingTokenControllerAbi from '../abis/bondingTokenController.abi.json';
import StakingControllerAbi from '../abis/stakingController.abi.json';
import BondingFactoryAbi from '../abis/bondingFactory.abi.json';

export interface IContract {
  name: string;
  address: string;
  events: string[];
  startBlock: number;
  abi: ContractAbi;
}

export const contractsConfig: IContract[] = [
  {
    name: 'BondingTokenController',
    address: process.env.BONDINGTOKENCONTROLLER, //Bonding Token Controller Proxy
    events: ['BuyTokens', 'SellTokens'],
    startBlock: Number(process.env.START_BLOCK) | 0,
    abi: BondingTokenControllerAbi,
  },
  {
    name: 'StakingControllerProxy',
    address: process.env.STAKINGCONTROLLERPROXY, //Staking Controller Proxy
    events: ['StakingContractCreated', 'TokenStaked', 'TokenUnstaked'],
    startBlock: Number(process.env.START_BLOCK) | 0,
    abi: StakingControllerAbi,
  },
  {
    name: 'BondingFactory',
    address: process.env.BONDINGFACTORYPROXY,
    events: [],
    startBlock: Number(process.env.START_BLOCK) | 0,
    abi: BondingFactoryAbi,
  },
];
