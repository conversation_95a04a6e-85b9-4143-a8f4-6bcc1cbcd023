{"BuyTokens": {"type": "BuyTokens", "token": "token", "amount": "amount", "tradeID": "tradeID", "userAddress": "userAddress", "transactionHash": "transactionHash"}, "SellTokens": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "token": "token", "amount": "amount", "tradeID": "tradeID", "userAddress": "userAddress", "transactionHash": "transactionHash"}, "StakingContractCreated": {"type": "StakingContractCreated", "bondingToken": "bondingToken", "stakingContract": "stakingContract", "creationTime": "creationTime", "salt": "salt", "transactionHash": "transactionHash"}, "TokenStaked": {"type": "TokenStaked", "token": "token", "userAddress": "user", "stakedAmount": "stakedAmount", "userTotalStaked": "userTotalStaked", "totalTokenStaked": "totalTokenStaked", "timestamp": "timestamp", "actionID": "actionID", "transactionHash": "transactionHash"}, "TokenUnstaked": {"type": "TokenUnstaked", "token": "token", "userAddress": "user", "unstakedAmount": "unStakedAmount", "userTotalStaked": "userTotalStaked", "totalTokenStaked": "totalTokenStaked", "timestamp": "timestamp", "actionID": "actionID", "transactionHash": "transactionHash"}, "DividendPayout": {"type": "DividendPayout", "caller": "caller", "stablecoin": "stablecoin", "bondingToken": "bondingToken", "to": "to", "amounts": "amounts", "actionIDs": "actionIDs", "transactionHash": "transactionHash"}}