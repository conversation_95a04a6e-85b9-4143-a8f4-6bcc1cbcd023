import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { <PERSON><PERSON>r<PERSON><PERSON><PERSON> } from '../helpers/sentry.helper';

const SECRET_KEY = process.env.HMAC_SECRET || 'your-secret-key';

export function hmacAuth(req: Request, res: Response, next: NextFunction): void {
  try {
    const signature = req.headers['x-hmac-signature'] as string;
    if (!signature) {
      ErrorHandler.handleWarning('Missing HMAC signature', {
        path: req.path,
        method: req.method,
      });
      res.status(401).json({ error: 'Missing HMAC signature' });
      return;
    }
    const bodyString = JSON.stringify(req.body);
    const expectedSignature = crypto.createHmac('sha256', SECRET_KEY).update(bodyString).digest('hex');
    if (signature !== expectedSignature) {
      ErrorHandler.handleWarning('Invalid HMAC signature', {
        path: req.path,
        method: req.method,
        providedSignature: signature,
        expectedSignature,
      });
      res.status(403).json({ error: 'Invalid HMAC signature' });
      return;
    }
    next();
  } catch (error) {
    ErrorHandler.handleError(error as Error, {
      context: 'hmacAuth',
      path: req.path,
      method: req.method,
    });
    next(error);
  }
}
