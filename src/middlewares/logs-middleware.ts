import { errorServiceResponse, successServiceResponse } from '../common/response.handler';
import { LOGS_MESSAGE } from '../common/return-message';
import { Logs } from '../models/logs.model';
import { ErrorHandler } from '../helpers/sentry.helper';

export const createLogs = async (logsData: any) => {
  try {
    const { controller, service, functionName, data, payload, message = '' } = logsData;
    const insert = { controller, service, functionName, data, payload, message };
    const inserted = await Logs.create(insert);
    ErrorHandler.handleInfo('Log created successfully', {
      controller,
      service,
      functionName,
    });
    return successServiceResponse(LOGS_MESSAGE.INSERTED, inserted);
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'createLogs',
      logsData,
    });
    return errorServiceResponse(error.message);
  }
};
