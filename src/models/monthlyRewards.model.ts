import { Schema, model, Document } from 'mongoose';

export interface IMonthlyTokenReward extends Document {
  month: string; // Format: "YYYY-MM"
  token: string; // Token contract address
  userAddress: string; // User wallet address
  averageStaked: number; // User's monthly average staked amount
  totalAverageStaked: number; // Total average staked across all users for this token/month
  userReward: number; // Calculated reward amount for user (0 until admin sets total)
  totalRewardAmount: number; // Admin-set total reward pool for this token/month
  rewardCalculated: boolean; // false initially, true after admin input
  calculatedAt: Date; // When average was calculated
  rewardDistributed: boolean;
  onchainAmount: string;
}

const monthlyTokenRewardSchema = new Schema<IMonthlyTokenReward>(
  {
    month: { type: String, required: true, index: true },
    token: { type: String, required: true, index: true },
    userAddress: { type: String, required: true, index: true },
    averageStaked: { type: Number, required: true, default: 0 },
    totalAverageStaked: { type: Number, required: true, default: 0 },
    userReward: { type: Number, required: true, default: 0 },
    totalRewardAmount: { type: Number, required: true, default: 0 },
    rewardCalculated: { type: Boolean, required: true, default: false },
    calculatedAt: { type: Date, required: true, default: Date.now },
    rewardDistributed: { type: Boolean, required: true, default: false },
    onchainAmount: { type: String, required: false },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Compound indexes for efficient queries
monthlyTokenRewardSchema.index({ month: 1, token: 1 });
monthlyTokenRewardSchema.index({ month: 1, token: 1, userAddress: 1 }, { unique: true });
monthlyTokenRewardSchema.index({ rewardCalculated: 1, month: 1 });

export default model<IMonthlyTokenReward>('monthlyTokenRewards', monthlyTokenRewardSchema);
