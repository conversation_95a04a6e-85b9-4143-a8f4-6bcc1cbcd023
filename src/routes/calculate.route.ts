import { Router, Request, Response } from 'express';
import monthlyRewardsService from '../services/monthlyRewards.service';

const router = Router();

/**
 * @swagger
 * /calculate:
 *   get:
 *     summary: Calculate endpoint
 *     description: Simple calculation endpoint for testing
 *     tags: [Calculate]
 *     responses:
 *       200:
 *         description: Calculate function executed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Calculate function executed successfully"
 */
router.get('/calculate', async (req: Request, res: Response) => {
  console.log('i am a calculate function');
  await monthlyRewardsService.calculateMonthlyAverages();
  res.status(200).json({
    message: 'Calculate function executed successfully',
    timestamp: new Date().toISOString(),
  });
});

/**
 * @swagger
 * /test-monthly-calculation:
 *   post:
 *     summary: Test monthly calculation
 *     description: Test the updated monthly calculation logic with userTotalStaked
 *     tags: [Calculate]
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               targetMonth:
 *                 type: string
 *                 example: "2025-01"
 *                 description: Month to calculate in YYYY-MM format
 *     responses:
 *       200:
 *         description: Monthly calculation test completed successfully
 */
router.post('/test-monthly-calculation', async (req: Request, res: Response) => {
  try {
    console.log('Testing monthly calculation with updated userTotalStaked logic...');

    const { targetMonth } = req.body;
    const month = targetMonth || '2025-01'; // Default test month

    await monthlyRewardsService.calculateMonthlyAverages(month);

    res.status(200).json({
      message: 'Monthly calculation test completed successfully',
      description: 'Now uses userTotalStaked for rewards and includes users with no activity in the month',
      month: month,
      timestamp: new Date().toISOString(),
    });
  } catch (error: any) {
    console.error('Error in test monthly calculation:', error);
    res.status(500).json({
      message: 'Error in monthly calculation test',
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
