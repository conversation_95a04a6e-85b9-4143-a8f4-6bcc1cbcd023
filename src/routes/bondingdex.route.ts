import * as express from 'express';
import { getBondingdexs, listBondingdexs, registerBondingdex, getTokenHolders, getTokenChartDataController } from '../controllers/bondingdex.controller';
import { hmacAuth } from '../middlewares/hmac-middleware';
import { validateRequest } from '../validations/validate';
import { bondingdexZodSchema } from '../validations/BondingdexValidation';

const router = express.Router();

/**
 * @swagger
 * /bonding/api/bondingdex/create:
 *   post:
 *     summary: Create a new bonding DEX
 *     description: Create a new bonding DEX with the provided details
 *     security:
 *       - hmacAuth: []
 *     tags:
 *       - Bonding DEX
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Bondingdex'
 *     responses:
 *       200:
 *         description: Bonding DEX created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Invalid request body
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Missing HMAC signature
 *       403:
 *         description: Invalid HMAC signature
 *       500:
 *         description: Internal server error
 */
router.post('/bondingdex/create', hmacAuth, validateRequest(bondingdexZodSchema), registerBondingdex);

/**
 * @swagger
 * /bonding/api/bondingdex-list:
 *   get:
 *     summary: List all bonding DEXs
 *     description: Get a list of all bonding DEXs with pagination
 *     tags:
 *       - Bonding DEX
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of bonding DEXs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     count:
 *                       type: integer
 *                     rows:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Bondingdex'
 *       500:
 *         description: Internal server error
 */
router.get('/bondingdex-list', listBondingdexs);

/**
 * @swagger
 * /bonding/api/bondingdex:
 *   get:
 *     summary: Get bonding DEX by ID
 *     description: Get details of a specific bonding DEX by its ID
 *     tags:
 *       - Bonding DEX
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Bonding DEX ID
 *     responses:
 *       200:
 *         description: Bonding DEX details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: ID is required
 *       404:
 *         description: Bonding DEX not found
 *       500:
 *         description: Internal server error
 */
router.get('/bondingdex', getBondingdexs);

/**
 * @swagger
 * /bonding/api/tokenChart/{tokenAddress}:
 *   get:
 *     summary: Get token chart data
 *     description: Get historical chart data for a specific token
 *     tags:
 *       - Token Data
 *     parameters:
 *       - in: path
 *         name: tokenAddress
 *         required: true
 *         schema:
 *           type: string
 *         description: Token contract address
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [1d, 7d, 1m, 1y, all]
 *           default: 1d
 *         description: Time period for chart data
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Token chart data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Token address is required
 *       500:
 *         description: Internal server error
 */
router.get('/tokenChart/:tokenAddress', getTokenChartDataController);

/**
 * @swagger
 * /bonding/api/token-holders:
 *   get:
 *     summary: Get token holders
 *     description: Get list of token holders with pagination
 *     tags:
 *       - Token Data
 *     parameters:
 *       - in: query
 *         name: contractAddress
 *         required: true
 *         schema:
 *           type: string
 *         description: Token contract address
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Token holders retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Contract address is required
 *       500:
 *         description: Internal server error
 */
router.get('/token-holders', getTokenHolders);

export default router;
