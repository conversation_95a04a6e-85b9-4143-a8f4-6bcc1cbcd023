import Web3 from 'web3';
import { AbiItem } from 'web3-utils';
import { Contract } from 'web3';
import { Error<PERSON>and<PERSON> } from './sentry.helper';
import { createLogs } from '../middlewares/logs-middleware';

export class Web3Helper {
  private web3: Web3;

  constructor(rpcUrl: string) {
    this.web3 = new Web3(rpcUrl);
  }

  public async getBlockNumber(): Promise<number> {
    try {
      const blockNumber = await this.web3.eth.getBlockNumber();
      console.log(`🔢 Current block number: ${blockNumber}`);
      return Number(blockNumber);
    } catch (error: any) {
      ErrorHandler.handleError(error, {
        context: 'web3.helper.getBlockNumber',
      });
      await createLogs({
        controller: 'web3.controller',
        message: error.message || 'Error getting block number',
        service: 'web3.helper',
        functionName: 'getBlockNumber',
        data: `'${error}'`,
        payload: JSON.stringify({}),
      });
      throw error;
    }
  }

  public async getBlock(blockNumber: number): Promise<any> {
    try {
      const block = await this.web3.eth.getBlock(blockNumber);
      console.log(`🔢 Block Info: ${block}`);
      return block;
    } catch (error: any) {
      ErrorHandler.handleError(error, {
        context: 'web3.helper.getBlock',
        params: { blockNumber },
      });
      await createLogs({
        controller: 'web3.controller',
        message: error.message || 'Error getting block',
        service: 'web3.helper',
        functionName: 'getBlock',
        data: `'${error}'`,
        payload: JSON.stringify({ blockNumber }),
      });
      throw error;
    }
  }

  public isAddress(address: string): boolean {
    return this.web3.utils.isAddress(address);
  }

  public createContractInstance<T extends AbiItem[]>(abi: T, address: string): Contract<T> {
    if (!this.isAddress(address)) {
      throw new Error(`Invalid contract address: ${address}`);
    }

    const contract = new this.web3.eth.Contract(abi, address);
    // console.log(`✅ Contract instance created at ${address}`);
    return contract;
  }

  public async getPastEvents<T extends AbiItem[]>(contract: Contract<T>, fromBlock: number, toBlock: number) {
    try {
      const events = await contract.getPastEvents('allEvents', {
        fromBlock,
        toBlock,
      });
      return events;
    } catch (error: any) {
      ErrorHandler.handleError(error, {
        context: 'web3.helper.getPastEvents',
        params: { fromBlock, toBlock },
      });
      await createLogs({
        controller: 'web3.controller',
        message: error.message || 'Error getting past events',
        service: 'web3.helper',
        functionName: 'getPastEvents',
        data: `'${error}'`,
        payload: JSON.stringify({ fromBlock, toBlock }),
      });
      throw error;
    }
  }

  /**
   * Get `from` and `to` address from a transaction hash
   * @param txHash - Ethereum transaction hash
   * @returns Object with from and to address
   */
  public getTransactionAddresses = async (txHash: string): Promise<string | null> => {
    try {
      const tx = await this.web3.eth.getTransaction(txHash);

      if (!tx) {
        ErrorHandler.handleWarning('Transaction not found', { txHash });
        throw new Error('Transaction not found');
      }

      return tx.from;
    } catch (error) {
      ErrorHandler.handleError(error as Error, {
        context: 'getTransactionAddresses',
        txHash,
      });
      throw new Error('Failed to fetch transaction details');
    }
  };
}
