import * as Sentry from '@sentry/node';
import { ProfilingIntegration } from '@sentry/profiling-node';

class SentryService {
  private initialized = false;

  public init() {
    if (!process.env.SENTRY_DSN) {
      console.warn('⚠️ Sentry DSN not provided. Error tracking will be disabled.');
      return;
    }

    if (this.initialized) {
      console.warn('⚠️ Sentry already initialized');
      return;
    }

    try {
      console.log(' process.env.SENTRY_DSN', process.env.SENTRY_DSN);
      Sentry.init({
        dsn: process.env.SENTRY_DSN,
        environment: process.env.SENTRY_ENVIRONMENT || 'development',
        release: process.env.SENTRY_RELEASE || '1.0.0',
        integrations: [new ProfilingIntegration()],
        tracesSampleRate: 1.0,
        profilesSampleRate: 1.0,
      });

      this.initialized = true;
      console.log('✅ Sentry initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Sentry:', error);
    }
  }

  public isInitialized(): boolean {
    return this.initialized;
  }

  public captureError(error: Error, context?: Record<string, any>) {
    if (!this.initialized) {
      console.error('❌ Sentry not initialized. Error:', error);
      return;
    }

    try {
      if (context) {
        Sentry.withScope((scope) => {
          Object.entries(context).forEach(([key, value]) => {
            scope.setExtra(key, value);
          });
          Sentry.captureException(error);
        });
      } else {
        Sentry.captureException(error);
      }
    } catch (sentryError) {
      console.error('❌ Failed to capture error with Sentry:', sentryError);
    }
  }

  public captureMessage(message: string, level: Sentry.SeverityLevel = 'info', context?: Record<string, any>) {
    if (!this.initialized) {
      console.log('⚠️ Sentry not initialized. Message:', message);
      return;
    }

    try {
      if (context) {
        Sentry.withScope((scope) => {
          Object.entries(context).forEach(([key, value]) => {
            scope.setExtra(key, value);
          });
          Sentry.captureMessage(message, level);
        });
      } else {
        Sentry.captureMessage(message, level);
      }
    } catch (sentryError) {
      console.error('❌ Failed to capture message with Sentry:', sentryError);
    }
  }

  public setUser(user: { id?: string; email?: string; username?: string }) {
    if (!this.initialized) return;

    try {
      Sentry.setUser(user);
    } catch (error) {
      console.error('❌ Failed to set Sentry user:', error);
    }
  }

  public setTag(key: string, value: string) {
    if (!this.initialized) return;

    try {
      Sentry.setTag(key, value);
    } catch (error) {
      console.error('❌ Failed to set Sentry tag:', error);
    }
  }

  public setContext(key: string, context: Record<string, any>) {
    if (!this.initialized) return;

    try {
      Sentry.setContext(key, context);
    } catch (error) {
      console.error('❌ Failed to set Sentry context:', error);
    }
  }

  public addBreadcrumb(breadcrumb: Sentry.Breadcrumb) {
    if (!this.initialized) return;

    try {
      Sentry.addBreadcrumb({
        ...breadcrumb,
        timestamp: Date.now() / 1000,
      });
    } catch (error) {
      console.error('❌ Failed to add Sentry breadcrumb:', error);
    }
  }

  public async close(timeout?: number) {
    if (!this.initialized) return;

    try {
      return await Sentry.close(timeout);
    } catch (error) {
      console.error('❌ Failed to close Sentry:', error);
    }
  }
}

export default new SentryService();

// Common error handling functions that abstract Sentry implementation
export class ErrorHandler {
  private static sentryService = new SentryService();

  public static handleError(error: Error, context?: Record<string, any>) {
    // Show only error message, not full stack trace
    console.error('\n\nError ====>>\n\n', error.message || error);

    // Send to Sentry if available
    if (this.sentryService.isInitialized()) {
      this.sentryService.captureError(error, context);
    }
  }

  public static handleWarning(message: string, context?: Record<string, any>) {
    console.warn('Warning:', message);

    if (this.sentryService.isInitialized()) {
      this.sentryService.captureMessage(message, 'warning', context);
    }
  }

  public static handleInfo(message: string, context?: Record<string, any>) {
    console.info('Info:', message);

    if (this.sentryService.isInitialized()) {
      this.sentryService.captureMessage(message, 'info', context);
    }
  }
}
