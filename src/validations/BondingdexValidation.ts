import { z } from 'zod';
import { offeringStatusEnum } from '../utils/common.interface';

// Custom Reusable Schemas
const authorizedCountrySchema = z
  .object({
    name: z.string(),
    isoCode: z.string(),
    countryCode: z.string(),
  })
  .strict();

const customFieldSchema = z
  .object({
    label: z.string(),
    type: z.string(),
    value: z.string(),
  })
  .strict();

const customDocSchema = z
  .object({
    docsLabel: z.string(),
    value: z.string(),
  })
  .strict();

const teamMemberSchema = z
  .object({
    name: z.string(),
    title: z.string(),
    summary: z.string().optional(),
    email: z.string().optional(),
    url: z.string().optional(),
    linkedInUrl: z.string().optional(),
    twitterUrl: z.string().optional(),
  })
  .strict();

const acquisitionCostsSchema = z
  .object({
    legalCost: z.number().optional(),
    estateAgent: z.string().optional(),
    stampDuty: z.string().optional(),
    tax: z.string().optional(),
    platformFees: z.number().optional(),
    maintenance: z.string().optional(),
    total: z.number().optional(),
  })
  .strict();

const overviewSchema = z
  .object({
    title: z.string(),
    subTitle: z.string().optional(),
    description: z.string(),
    entityName: z.string(),
    entityType: z.string(),
    webUrl: z.string().optional(),
    lineOfBusiness: z.string(),
    sourceOfFunds: z.string(),
    location: z.string(),
    companyDescription: z.string(),
    icon: z.string().optional(),
    cover: z.string().optional(),
    logo: z.string(),
    propertyImages: z.array(z.string()).optional(),
  })
  .strict();

export const bondingdexZodSchema = z.object({
  _id: z.string().optional(),
  userId: z.string(),
  createdBy: z.string().optional(),
  createdAt: z.string().optional().or(z.date().optional()),
  updatedAt: z.string().optional().or(z.date().optional()),
  currentStep: z.number(),

  overview: overviewSchema,

  projectDetails: z
    .object({
      assetType: z.string(),
      blockChainType: z.string(),
      offeringType: z.string(),
      tokenStandard: z.string(),
      offeringName: z.string(),
      CUSIP: z.string().optional(),
      isAuthorized: z.boolean().optional(),
      authorizedCountries: z.array(authorizedCountrySchema).optional(),
      startDate: z.union([z.string(), z.date()]).optional(),
      endDate: z.union([z.string(), z.date()]).optional(),
      minInvestment: z.number(),
      maxInvestment: z.number(),
      assetName: z.string(),
      tokenTicker: z.string().optional(),
      tokenSupply: z.number(),
      tokenDecimals: z.number(),
      lockupMonths: z.number().optional(),
      holdTime: z.union([z.string(), z.date()]),
      maxTokenHolding: z.number(),
      navLaunchPrice: z.number().optional(),
      latestNav: z.number().optional(),
      isTransferAgent: z.boolean(),
      taId: z.string().optional(),
      issuerId: z.string(),
      issuerWallet: z.string(),
      isPrivate: z.boolean(),
      offeringMembers: z.array(z.string()).optional(),
      customFields: z.array(customFieldSchema).optional(),
      propertyType: z.string().optional(),
      propertySubtype: z.string().optional(),
      yearBuilt: z.number().min(1).max(9999).optional(),
      lotSize: z.number().min(1).optional(),
      occupancy: z.number().min(0).max(100).optional(),
      projectedYield: z.number().min(0).max(100),
      launchValuation: z.number().min(0).optional(),
      previousValuation: z.number().min(0).optional(),
      deRatio: z.number().min(0).optional(),
      acquisitionCosts: acquisitionCostsSchema.optional(),
      agentName: z.string().optional(),
      agentFunctions: z.array(z.string()).optional(),
      poweredBy: z.string().optional(),
      poweredByLogo: z.string().optional(),
      bondingPrice: z.number().optional(),
      roi: z.number().optional(),
    })
    .strict(),

  documents: z
    .object({
      eSign: z.string().optional(),
      pitchDeck: z.string().optional(),
      confidentialInformationMemorendum: z.string().optional(),
      landRegistration: z.string().optional(),
      titleDocs: z.string().optional(),
      bankApproval: z.string().optional(),
      encumbranceCertificate: z.string().optional(),
      propertyTaxReceipt: z.string().optional(),
      articlesOfAssociation: z.string().optional(),
      operatingAgreement: z.string().optional(),
      taxAssignmentLetter: z.string().optional(),
      certificateOfRegistration: z.string().optional(),
      registerOfManagers: z.string().optional(),
      customDocs: z.array(customDocSchema).optional(),
    })
    .strict()
    .optional(),

  team: z.array(teamMemberSchema).optional(),
  template_id: z.string().optional(),
  envelopeId: z.string().optional(),

  fee: z
    .object({
      escrowFee: z.number().optional(),
      wrapFee: z.number().optional(),
      dividendFee: z.number().optional(),
      redemptionFee: z.number().optional(),
    })
    .strict()
    .optional(),

  tokenAddress: z.string().optional(),
  isTokenDeploy: z.boolean(),
  erc20Address: z.string().optional(),
  iserc20: z.boolean(),
  isFundDeploy: z.boolean(),
  identityRegistry: z.string().optional(),
  fundAddress: z.string().optional(),
  deployedDate: z.union([z.string(), z.date()]).optional(),
  wrapperDeployedAt: z.union([z.string(), z.date()]).optional(),

  isActive: z.boolean(),
  isDelete: z.boolean(),
  isFinalSubmission: z.boolean(),
  status: z.enum(Object.values(offeringStatusEnum) as [string, ...string[]]).optional(),
  offeringFeeStatus: z.boolean().optional(),
  proposalHoldingPercentage: z.array(z.string()).optional(),
  reason: z.string().optional(),
  isBondingDeploy: z.boolean(),
  isNft: z.boolean(),
  bondingAddress: z.string().optional(),
  isSchedule: z.boolean().optional(),
  scheduleTime: z.number().optional(),
});
// .strict();
