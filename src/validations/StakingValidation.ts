import { z } from 'zod';

// Validation for pagination query parameters
export const paginationQuerySchema = z.object({
  page: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 1)),
  limit: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 10)),
  search: z.string().optional().default(''),
});

// Validation for staking transactions query parameters
export const stakingTransactionsQuerySchema = paginationQuerySchema.extend({
  userAddress: z.string().optional(),
  type: z.string().optional(),
});

// Validation for token parameter
export const tokenParamSchema = z.object({
  token: z.string().min(1, 'Token address is required'),
});

// Validation for user address parameter
export const userAddressParamSchema = z.object({
  userAddress: z.string().regex(/^0x[a-fA-F0-9]{40}$/, 'Invalid Ethereum address format'),
});

// Combined validation schemas for each endpoint
export const getStakingTransactionsValidation = {
  params: tokenParamSchema,
  query: stakingTransactionsQuerySchema,
};

export const getUserStakingSummaryValidation = {
  params: tokenParamSchema,
  query: paginationQuerySchema,
};

export const getUserStakedOfferingsValidation = {
  params: userAddressParamSchema,
  query: paginationQuerySchema,
};
