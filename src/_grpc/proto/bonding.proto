syntax = "proto3";

package bonding;

// Pagination message
message Pagination {
  int32 page = 1;
  int32 limit = 2;
  string search = 3;
}

// Staking Transaction message
message StakingTransaction {
  string type = 1; // TokenStaked or TokenUnstaked
  string token = 2;
  string userAddress = 3;
  string stakedAmount = 4;
  string unstakedAmount = 5;
  string userTotalStaked = 6;
  string totalTokenStaked = 7;
  int64 timestamp = 8;
  string actionID = 9;
  string transactionHash = 10;
  string _id = 11;
}

// User staking summary for table
message UserStakingSummary {
  string userAddress = 1;
  string lastActivity = 2; // Stake/Unstake
  int64 lastActivityTime = 3;
  string totalStakedAmount = 4;
  string averageAmount = 5;
  string _id = 6;

}

// Staking dividend record for monthly rewards
message StakingDividend {
  string month = 1; // Format: "YYYY-MM"
  string token = 2; // Token contract address
  string userAddress = 3; // User wallet address
  string averageStaked = 4; // User's monthly average staked amount
  string totalAverageStaked = 5; // Total average staked across all users for this token/month
  string userReward = 6; // Calculated reward amount for user
  string totalRewardAmount = 7; // Admin-set total reward pool for this token/month
  bool rewardCalculated = 8; // Whether reward has been calculated
  int64 calculatedAt = 9; // When average was calculated (timestamp)
  int64 createdAt = 10; // Created timestamp
  int64 updatedAt = 11; // Updated timestamp
  string _id = 12;
  bool rewardDistributed = 13;
  string onchainAmount = 14;
}

// Request for staking transactions
message StakingTransactionsRequest {
  string token = 1;
  Pagination pagination = 2;
  string userAddress = 3;
  string type = 4;
}

// Response for staking transactions
message StakingTransactionsResponse {
  repeated StakingTransaction transactions = 1;
  int32 currentPage = 2;
  int32 totalPages = 3;
  int32 totalCount = 4;
  int32 nextPage = 5;
  int32 previousPage = 6;
}

// Request for user staking summary
message UserStakingSummaryRequest {
  string token = 1;
  Pagination pagination = 2;
}

// Response for user staking summary
message UserStakingSummaryResponse {
  repeated UserStakingSummary users = 1;
  int32 currentPage = 2;
  int32 totalPages = 3;
  int32 totalCount = 4;
  int32 nextPage = 5;
  int32 previousPage = 6;
}

// Request for staking dividend list
message StakingDividendListRequest {
  string token = 1;
  string monthString = 2; // Month in format "YYYY-MM-DD" or "YYYY-MM"
  Pagination pagination = 3;
}

// Response for staking dividend list
message StakingDividendListResponse {
  repeated StakingDividend data = 1;
  int32 currentPage = 2;
  int32 totalPages = 3;
  int32 totalCount = 4;
  int32 nextPage = 5;
  int32 previousPage = 6;
}

// Request for updating staking reward amount
message UpdateStakingRewardAmountRequest {
  string token = 1;
  string month = 2; // Month in format "YYYY-MM"
  double totalRewardAmount = 3; // Total reward amount to distribute
}

// Response for updating staking reward amount
message UpdateStakingRewardAmountResponse {
  bool success = 1;
  string message = 2;
  int32 updatedCount = 3; // Number of records updated
  double totalRewardAmount = 4; // The amount that was set
}

service BondingService {
  // Get paginated staking transactions for a token
  rpc GetStakingTransactions(StakingTransactionsRequest) returns (StakingTransactionsResponse);
  // Get paginated user staking summary for a token
  rpc GetUserStakingSummary(UserStakingSummaryRequest) returns (UserStakingSummaryResponse);
  // Get paginated staking dividend list for a token and month
  rpc GetStakingDividendList(StakingDividendListRequest) returns (StakingDividendListResponse);
  // Update staking reward amount for a token and month
  rpc UpdateStakingRewardAmount(UpdateStakingRewardAmountRequest) returns (UpdateStakingRewardAmountResponse);
} 